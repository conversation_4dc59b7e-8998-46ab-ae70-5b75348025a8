# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/
env/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Database
*.db
*.sqlite3

# Logs
logs/
*.log

# Environment variables
.env

# Cache
__pycache__/
.pytest_cache/
.coverage
htmlcov/

# ML models (optional, depending on your strategy)
# models/

# Data files (optional, depending on your strategy)
# data/

# Temporary files
.DS_Store
Thumbs.db
