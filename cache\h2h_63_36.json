{"data": [{"fixture": {"id": 868261, "referee": "<PERSON><PERSON>", "timezone": "UTC", "date": "2023-04-22T11:30:00+00:00", "timestamp": 1682163000, "periods": {"first": 1682163000, "second": 1682166600}, "venue": {"id": 535, "name": "Craven Cottage", "city": "London"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 39, "name": "Premier League", "country": "England", "logo": "https://media.api-sports.io/football/leagues/39.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2022, "round": "Regular Season - 32", "standings": true}, "teams": {"home": {"id": 36, "name": "Fulham", "logo": "https://media.api-sports.io/football/teams/36.png", "winner": true}, "away": {"id": 63, "name": "Leeds", "logo": "https://media.api-sports.io/football/teams/63.png", "winner": false}}, "goals": {"home": 2, "away": 1}, "score": {"halftime": {"home": 0, "away": 0}, "fulltime": {"home": 2, "away": 1}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}, {"fixture": {"id": 1004838, "referee": "<PERSON><PERSON>", "timezone": "UTC", "date": "2023-02-28T19:45:00+00:00", "timestamp": 1677613500, "periods": {"first": 1677613500, "second": 1677617100}, "venue": {"id": 535, "name": "Craven Cottage", "city": "London"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 45, "name": "FA Cup", "country": "England", "logo": "https://media.api-sports.io/football/leagues/45.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2022, "round": "5th Round", "standings": false}, "teams": {"home": {"id": 36, "name": "Fulham", "logo": "https://media.api-sports.io/football/teams/36.png", "winner": true}, "away": {"id": 63, "name": "Leeds", "logo": "https://media.api-sports.io/football/teams/63.png", "winner": false}}, "goals": {"home": 2, "away": 0}, "score": {"halftime": {"home": 1, "away": 0}, "fulltime": {"home": 2, "away": 0}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}, {"fixture": {"id": 868069, "referee": "<PERSON><PERSON>", "timezone": "UTC", "date": "2022-10-23T13:00:00+00:00", "timestamp": 1666530000, "periods": {"first": 1666530000, "second": 1666533600}, "venue": {"id": 546, "name": "Elland Road", "city": "Leeds, West Yorkshire"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 39, "name": "Premier League", "country": "England", "logo": "https://media.api-sports.io/football/leagues/39.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2022, "round": "Regular Season - 13", "standings": true}, "teams": {"home": {"id": 63, "name": "Leeds", "logo": "https://media.api-sports.io/football/teams/63.png", "winner": false}, "away": {"id": 36, "name": "Fulham", "logo": "https://media.api-sports.io/football/teams/36.png", "winner": true}}, "goals": {"home": 2, "away": 3}, "score": {"halftime": {"home": 1, "away": 1}, "fulltime": {"home": 2, "away": 3}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}, {"fixture": {"id": 787051, "referee": "<PERSON><PERSON>", "timezone": "UTC", "date": "2021-09-21T18:45:00+00:00", "timestamp": 1632249900, "periods": {"first": 1632249900, "second": 1632253500}, "venue": {"id": 535, "name": "Craven Cottage", "city": "London"}, "status": {"long": "Match Finished", "short": "PEN", "elapsed": 120, "extra": null}}, "league": {"id": 48, "name": "League Cup", "country": "England", "logo": "https://media.api-sports.io/football/leagues/48.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2021, "round": "3rd Round", "standings": false}, "teams": {"home": {"id": 36, "name": "Fulham", "logo": "https://media.api-sports.io/football/teams/36.png", "winner": false}, "away": {"id": 63, "name": "Leeds", "logo": "https://media.api-sports.io/football/teams/63.png", "winner": true}}, "goals": {"home": 0, "away": 0}, "score": {"halftime": {"home": 0, "away": 0}, "fulltime": {"home": 0, "away": 0}, "extratime": {"home": 0, "away": 0}, "penalty": {"home": 5, "away": 6}}}, {"fixture": {"id": 592779, "referee": "<PERSON><PERSON>", "timezone": "UTC", "date": "2021-03-19T20:00:00+00:00", "timestamp": 1616184000, "periods": {"first": 1616184000, "second": 1616187600}, "venue": {"id": 535, "name": "Craven Cottage", "city": "London"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 39, "name": "Premier League", "country": "England", "logo": "https://media.api-sports.io/football/leagues/39.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2020, "round": "Regular Season - 29", "standings": true}, "teams": {"home": {"id": 36, "name": "Fulham", "logo": "https://media.api-sports.io/football/teams/36.png", "winner": false}, "away": {"id": 63, "name": "Leeds", "logo": "https://media.api-sports.io/football/teams/63.png", "winner": true}}, "goals": {"home": 1, "away": 2}, "score": {"halftime": {"home": 1, "away": 1}, "fulltime": {"home": 1, "away": 2}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}, {"fixture": {"id": 592155, "referee": "<PERSON><PERSON>", "timezone": "UTC", "date": "2020-09-19T14:00:00+00:00", "timestamp": 1600524000, "periods": {"first": 1600524000, "second": 1600527600}, "venue": {"id": 546, "name": "Elland Road", "city": "Leeds, West Yorkshire"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 39, "name": "Premier League", "country": "England", "logo": "https://media.api-sports.io/football/leagues/39.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2020, "round": "Regular Season - 2", "standings": true}, "teams": {"home": {"id": 63, "name": "Leeds", "logo": "https://media.api-sports.io/football/teams/63.png", "winner": true}, "away": {"id": 36, "name": "Fulham", "logo": "https://media.api-sports.io/football/teams/36.png", "winner": false}}, "goals": {"home": 4, "away": 3}, "score": {"halftime": {"home": 2, "away": 1}, "fulltime": {"home": 4, "away": 3}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}, {"fixture": {"id": 164637, "referee": "<PERSON><PERSON>", "timezone": "UTC", "date": "2020-06-27T14:00:00+00:00", "timestamp": 1593266400, "periods": {"first": 1593266400, "second": 1593270000}, "venue": {"id": 546, "name": "Elland Road", "city": "Leeds, West Yorkshire"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 40, "name": "Championship", "country": "England", "logo": "https://media.api-sports.io/football/leagues/40.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2019, "round": "Regular Season - 39", "standings": true}, "teams": {"home": {"id": 63, "name": "Leeds", "logo": "https://media.api-sports.io/football/teams/63.png", "winner": true}, "away": {"id": 36, "name": "Fulham", "logo": "https://media.api-sports.io/football/teams/36.png", "winner": false}}, "goals": {"home": 3, "away": 0}, "score": {"halftime": {"home": 1, "away": 0}, "fulltime": {"home": 3, "away": 0}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}, {"fixture": {"id": 164440, "referee": "<PERSON><PERSON>", "timezone": "UTC", "date": "2019-12-21T15:00:00+00:00", "timestamp": 1576940400, "periods": {"first": 1576940400, "second": 1576944000}, "venue": {"id": 535, "name": "Craven Cottage", "city": "London"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 40, "name": "Championship", "country": "England", "logo": "https://media.api-sports.io/football/leagues/40.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2019, "round": "Regular Season - 23", "standings": true}, "teams": {"home": {"id": 36, "name": "Fulham", "logo": "https://media.api-sports.io/football/teams/36.png", "winner": true}, "away": {"id": 63, "name": "Leeds", "logo": "https://media.api-sports.io/football/teams/63.png", "winner": false}}, "goals": {"home": 2, "away": 1}, "score": {"halftime": {"home": 1, "away": 0}, "fulltime": {"home": 2, "away": 1}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}, {"fixture": {"id": 11988, "referee": "<PERSON>, England", "timezone": "UTC", "date": "2018-04-03T18:45:00+00:00", "timestamp": 1522781100, "periods": {"first": 1522781100, "second": 1522784700}, "venue": {"id": 535, "name": "Craven Cottage", "city": "London"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 40, "name": "Championship", "country": "England", "logo": "https://media.api-sports.io/football/leagues/40.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2017, "round": "Regular Season - 40", "standings": true}, "teams": {"home": {"id": 36, "name": "Fulham", "logo": "https://media.api-sports.io/football/teams/36.png", "winner": true}, "away": {"id": 63, "name": "Leeds", "logo": "https://media.api-sports.io/football/teams/63.png", "winner": false}}, "goals": {"home": 2, "away": 0}, "score": {"halftime": {"home": 1, "away": 0}, "fulltime": {"home": 2, "away": 0}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}, {"fixture": {"id": 12437, "referee": "<PERSON>, England", "timezone": "UTC", "date": "2017-08-15T18:45:00+00:00", "timestamp": 1502822700, "periods": {"first": 1502822700, "second": 1502826300}, "venue": {"id": null, "name": "Elland Road", "city": "Leeds"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 40, "name": "Championship", "country": "England", "logo": "https://media.api-sports.io/football/leagues/40.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2017, "round": "Regular Season - 3", "standings": true}, "teams": {"home": {"id": 63, "name": "Leeds", "logo": "https://media.api-sports.io/football/teams/63.png", "winner": null}, "away": {"id": 36, "name": "Fulham", "logo": "https://media.api-sports.io/football/teams/36.png", "winner": null}}, "goals": {"home": 0, "away": 0}, "score": {"halftime": {"home": 0, "away": 0}, "fulltime": {"home": 0, "away": 0}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}], "expires_at": 1747914555.323161}