{"data": [{"fixture": {"id": 1208089, "referee": "<PERSON><PERSON>", "timezone": "UTC", "date": "2024-10-05T14:00:00+00:00", "timestamp": 1728136800, "periods": {"first": 1728136800, "second": 1728140400}, "venue": {"id": 555, "name": "Etihad Stadium", "city": "Manchester"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": 6}}, "league": {"id": 39, "name": "Premier League", "country": "England", "logo": "https://media.api-sports.io/football/leagues/39.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2024, "round": "Regular Season - 7", "standings": true}, "teams": {"home": {"id": 50, "name": "Manchester City", "logo": "https://media.api-sports.io/football/teams/50.png", "winner": true}, "away": {"id": 36, "name": "Fulham", "logo": "https://media.api-sports.io/football/teams/36.png", "winner": false}}, "goals": {"home": 3, "away": 2}, "score": {"halftime": {"home": 1, "away": 1}, "fulltime": {"home": 3, "away": 2}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}, {"fixture": {"id": 1035537, "referee": "<PERSON><PERSON>", "timezone": "UTC", "date": "2024-05-11T11:30:00+00:00", "timestamp": 1715427000, "periods": {"first": 1715427000, "second": 1715430600}, "venue": {"id": 535, "name": "Craven Cottage", "city": "London"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 39, "name": "Premier League", "country": "England", "logo": "https://media.api-sports.io/football/leagues/39.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2023, "round": "Regular Season - 37", "standings": true}, "teams": {"home": {"id": 36, "name": "Fulham", "logo": "https://media.api-sports.io/football/teams/36.png", "winner": false}, "away": {"id": 50, "name": "Manchester City", "logo": "https://media.api-sports.io/football/teams/50.png", "winner": true}}, "goals": {"home": 0, "away": 4}, "score": {"halftime": {"home": 0, "away": 1}, "fulltime": {"home": 0, "away": 4}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}, {"fixture": {"id": 1035075, "referee": "<PERSON><PERSON>", "timezone": "UTC", "date": "2023-09-02T14:00:00+00:00", "timestamp": 1693663200, "periods": {"first": 1693663200, "second": 1693666800}, "venue": {"id": 555, "name": "Etihad Stadium", "city": "Manchester"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 39, "name": "Premier League", "country": "England", "logo": "https://media.api-sports.io/football/leagues/39.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2023, "round": "Regular Season - 4", "standings": true}, "teams": {"home": {"id": 50, "name": "Manchester City", "logo": "https://media.api-sports.io/football/teams/50.png", "winner": true}, "away": {"id": 36, "name": "Fulham", "logo": "https://media.api-sports.io/football/teams/36.png", "winner": false}}, "goals": {"home": 5, "away": 1}, "score": {"halftime": {"home": 2, "away": 1}, "fulltime": {"home": 5, "away": 1}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}, {"fixture": {"id": 868281, "referee": "<PERSON><PERSON>", "timezone": "UTC", "date": "2023-04-30T13:00:00+00:00", "timestamp": 1682859600, "periods": {"first": 1682859600, "second": 1682863200}, "venue": {"id": 535, "name": "Craven Cottage", "city": "London"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 39, "name": "Premier League", "country": "England", "logo": "https://media.api-sports.io/football/leagues/39.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2022, "round": "Regular Season - 34", "standings": true}, "teams": {"home": {"id": 36, "name": "Fulham", "logo": "https://media.api-sports.io/football/teams/36.png", "winner": false}, "away": {"id": 50, "name": "Manchester City", "logo": "https://media.api-sports.io/football/teams/50.png", "winner": true}}, "goals": {"home": 1, "away": 2}, "score": {"halftime": {"home": 1, "away": 2}, "fulltime": {"home": 1, "away": 2}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}, {"fixture": {"id": 868090, "referee": "D. England", "timezone": "UTC", "date": "2022-11-05T15:00:00+00:00", "timestamp": 1667660400, "periods": {"first": 1667660400, "second": 1667664000}, "venue": {"id": 555, "name": "Etihad Stadium", "city": "Manchester"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 39, "name": "Premier League", "country": "England", "logo": "https://media.api-sports.io/football/leagues/39.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2022, "round": "Regular Season - 15", "standings": true}, "teams": {"home": {"id": 50, "name": "Manchester City", "logo": "https://media.api-sports.io/football/teams/50.png", "winner": true}, "away": {"id": 36, "name": "Fulham", "logo": "https://media.api-sports.io/football/teams/36.png", "winner": false}}, "goals": {"home": 2, "away": 1}, "score": {"halftime": {"home": 1, "away": 1}, "fulltime": {"home": 2, "away": 1}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}, {"fixture": {"id": 824601, "referee": "<PERSON><PERSON>", "timezone": "UTC", "date": "2022-02-05T15:00:00+00:00", "timestamp": 1644073200, "periods": {"first": 1644073200, "second": 1644076800}, "venue": {"id": 555, "name": "Etihad Stadium", "city": "Manchester"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 45, "name": "FA Cup", "country": "England", "logo": "https://media.api-sports.io/football/leagues/45.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2021, "round": "4th Round", "standings": false}, "teams": {"home": {"id": 50, "name": "Manchester City", "logo": "https://media.api-sports.io/football/teams/50.png", "winner": true}, "away": {"id": 36, "name": "Fulham", "logo": "https://media.api-sports.io/football/teams/36.png", "winner": false}}, "goals": {"home": 4, "away": 1}, "score": {"halftime": {"home": 2, "away": 1}, "fulltime": {"home": 4, "away": 1}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}, {"fixture": {"id": 592769, "referee": "<PERSON><PERSON>", "timezone": "UTC", "date": "2021-03-13T20:00:00+00:00", "timestamp": 1615665600, "periods": {"first": 1615665600, "second": 1615669200}, "venue": {"id": 535, "name": "Craven Cottage", "city": "London"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 39, "name": "Premier League", "country": "England", "logo": "https://media.api-sports.io/football/leagues/39.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2020, "round": "Regular Season - 28", "standings": true}, "teams": {"home": {"id": 36, "name": "Fulham", "logo": "https://media.api-sports.io/football/teams/36.png", "winner": false}, "away": {"id": 50, "name": "Manchester City", "logo": "https://media.api-sports.io/football/teams/50.png", "winner": true}}, "goals": {"home": 0, "away": 3}, "score": {"halftime": {"home": 0, "away": 0}, "fulltime": {"home": 0, "away": 3}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}, {"fixture": {"id": 592246, "referee": "<PERSON><PERSON>", "timezone": "UTC", "date": "2020-12-05T15:00:00+00:00", "timestamp": 1607180400, "periods": {"first": 1607180400, "second": 1607184000}, "venue": {"id": 555, "name": "Etihad Stadium", "city": "Manchester"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 39, "name": "Premier League", "country": "England", "logo": "https://media.api-sports.io/football/leagues/39.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2020, "round": "Regular Season - 11", "standings": true}, "teams": {"home": {"id": 50, "name": "Manchester City", "logo": "https://media.api-sports.io/football/teams/50.png", "winner": true}, "away": {"id": 36, "name": "Fulham", "logo": "https://media.api-sports.io/football/teams/36.png", "winner": false}}, "goals": {"home": 2, "away": 0}, "score": {"halftime": {"home": 2, "away": 0}, "fulltime": {"home": 2, "away": 0}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}, {"fixture": {"id": 313299, "referee": "<PERSON><PERSON>", "timezone": "UTC", "date": "2020-01-26T13:00:00+00:00", "timestamp": 1580043600, "periods": {"first": 1580043600, "second": 1580047200}, "venue": {"id": 555, "name": "Etihad Stadium", "city": "Manchester"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 45, "name": "FA Cup", "country": "England", "logo": "https://media.api-sports.io/football/leagues/45.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2019, "round": "4th Round", "standings": false}, "teams": {"home": {"id": 50, "name": "Manchester City", "logo": "https://media.api-sports.io/football/teams/50.png", "winner": true}, "away": {"id": 36, "name": "Fulham", "logo": "https://media.api-sports.io/football/teams/36.png", "winner": false}}, "goals": {"home": 4, "away": 0}, "score": {"halftime": {"home": 2, "away": 0}, "fulltime": {"home": 4, "away": 0}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}, {"fixture": {"id": 380, "referee": "<PERSON>, England", "timezone": "UTC", "date": "2019-03-30T12:30:00+00:00", "timestamp": 1553949000, "periods": {"first": 1553949000, "second": 1553952600}, "venue": {"id": 535, "name": "Craven Cottage", "city": "London"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 39, "name": "Premier League", "country": "England", "logo": "https://media.api-sports.io/football/leagues/39.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2018, "round": "Regular Season - 32", "standings": true}, "teams": {"home": {"id": 36, "name": "Fulham", "logo": "https://media.api-sports.io/football/teams/36.png", "winner": false}, "away": {"id": 50, "name": "Manchester City", "logo": "https://media.api-sports.io/football/teams/50.png", "winner": true}}, "goals": {"home": 0, "away": 2}, "score": {"halftime": {"home": 0, "away": 2}, "fulltime": {"home": 0, "away": 2}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}], "expires_at": 1747914565.62815}