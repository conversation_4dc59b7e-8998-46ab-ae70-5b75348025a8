{"data": [{"fixture": {"id": 1035369, "referee": "<PERSON><PERSON>", "timezone": "UTC", "date": "2023-12-30T15:00:00+00:00", "timestamp": 1703948400, "periods": {"first": 1703948400, "second": 1703952000}, "venue": {"id": 555, "name": "Etihad Stadium", "city": "Manchester"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 39, "name": "Premier League", "country": "England", "logo": "https://media.api-sports.io/football/leagues/39.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2023, "round": "Regular Season - 20", "standings": true}, "teams": {"home": {"id": 50, "name": "Manchester City", "logo": "https://media.api-sports.io/football/teams/50.png", "winner": true}, "away": {"id": 62, "name": "Sheffield Utd", "logo": "https://media.api-sports.io/football/teams/62.png", "winner": false}}, "goals": {"home": 2, "away": 0}, "score": {"halftime": {"home": 1, "away": 0}, "fulltime": {"home": 2, "away": 0}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}, {"fixture": {"id": 1035066, "referee": "<PERSON><PERSON>", "timezone": "UTC", "date": "2023-08-27T13:00:00+00:00", "timestamp": 1693141200, "periods": {"first": 1693141200, "second": 1693144800}, "venue": {"id": 581, "name": "Bramall Lane", "city": "Sheffield"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 39, "name": "Premier League", "country": "England", "logo": "https://media.api-sports.io/football/leagues/39.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2023, "round": "Regular Season - 3", "standings": true}, "teams": {"home": {"id": 62, "name": "Sheffield Utd", "logo": "https://media.api-sports.io/football/teams/62.png", "winner": false}, "away": {"id": 50, "name": "Manchester City", "logo": "https://media.api-sports.io/football/teams/50.png", "winner": true}}, "goals": {"home": 1, "away": 2}, "score": {"halftime": {"home": 0, "away": 0}, "fulltime": {"home": 1, "away": 2}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}, {"fixture": {"id": 1017112, "referee": "<PERSON><PERSON>", "timezone": "UTC", "date": "2023-04-22T15:45:00+00:00", "timestamp": 1682178300, "periods": {"first": 1682178300, "second": 1682181900}, "venue": {"id": 489, "name": "Wembley Stadium", "city": "London"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 45, "name": "FA Cup", "country": "England", "logo": "https://media.api-sports.io/football/leagues/45.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2022, "round": "Semi-finals", "standings": false}, "teams": {"home": {"id": 50, "name": "Manchester City", "logo": "https://media.api-sports.io/football/teams/50.png", "winner": true}, "away": {"id": 62, "name": "Sheffield Utd", "logo": "https://media.api-sports.io/football/teams/62.png", "winner": false}}, "goals": {"home": 3, "away": 0}, "score": {"halftime": {"home": 1, "away": 0}, "fulltime": {"home": 3, "away": 0}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}, {"fixture": {"id": 592347, "referee": "<PERSON><PERSON>", "timezone": "UTC", "date": "2021-01-30T15:00:00+00:00", "timestamp": 1612018800, "periods": {"first": 1612018800, "second": 1612022400}, "venue": {"id": 555, "name": "Etihad Stadium", "city": "Manchester"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 39, "name": "Premier League", "country": "England", "logo": "https://media.api-sports.io/football/leagues/39.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2020, "round": "Regular Season - 21", "standings": true}, "teams": {"home": {"id": 50, "name": "Manchester City", "logo": "https://media.api-sports.io/football/teams/50.png", "winner": true}, "away": {"id": 62, "name": "Sheffield Utd", "logo": "https://media.api-sports.io/football/teams/62.png", "winner": false}}, "goals": {"home": 1, "away": 0}, "score": {"halftime": {"home": 1, "away": 0}, "fulltime": {"home": 1, "away": 0}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}, {"fixture": {"id": 592208, "referee": "<PERSON><PERSON>", "timezone": "UTC", "date": "2020-10-31T12:30:00+00:00", "timestamp": 1604147400, "periods": {"first": 1604147400, "second": 1604151000}, "venue": {"id": 581, "name": "Bramall Lane", "city": "Sheffield"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 39, "name": "Premier League", "country": "England", "logo": "https://media.api-sports.io/football/leagues/39.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2020, "round": "Regular Season - 7", "standings": true}, "teams": {"home": {"id": 62, "name": "Sheffield Utd", "logo": "https://media.api-sports.io/football/teams/62.png", "winner": false}, "away": {"id": 50, "name": "Manchester City", "logo": "https://media.api-sports.io/football/teams/50.png", "winner": true}}, "goals": {"home": 0, "away": 1}, "score": {"halftime": {"home": 0, "away": 1}, "fulltime": {"home": 0, "away": 1}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}, {"fixture": {"id": 157249, "referee": "<PERSON>, England", "timezone": "UTC", "date": "2020-01-21T19:30:00+00:00", "timestamp": 1579635000, "periods": {"first": 1579635000, "second": 1579638600}, "venue": {"id": 581, "name": "Bramall Lane", "city": "Sheffield"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 39, "name": "Premier League", "country": "England", "logo": "https://media.api-sports.io/football/leagues/39.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2019, "round": "Regular Season - 24", "standings": true}, "teams": {"home": {"id": 62, "name": "Sheffield Utd", "logo": "https://media.api-sports.io/football/teams/62.png", "winner": false}, "away": {"id": 50, "name": "Manchester City", "logo": "https://media.api-sports.io/football/teams/50.png", "winner": true}}, "goals": {"home": 0, "away": 1}, "score": {"halftime": {"home": 0, "away": 0}, "fulltime": {"home": 0, "away": 1}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}, {"fixture": {"id": 157209, "referee": "<PERSON>, England", "timezone": "UTC", "date": "2019-12-29T18:00:00+00:00", "timestamp": 1577642400, "periods": {"first": 1577642400, "second": 1577646000}, "venue": {"id": 555, "name": "Etihad Stadium", "city": "Manchester"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 39, "name": "Premier League", "country": "England", "logo": "https://media.api-sports.io/football/leagues/39.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2019, "round": "Regular Season - 20", "standings": true}, "teams": {"home": {"id": 50, "name": "Manchester City", "logo": "https://media.api-sports.io/football/teams/50.png", "winner": true}, "away": {"id": 62, "name": "Sheffield Utd", "logo": "https://media.api-sports.io/football/teams/62.png", "winner": false}}, "goals": {"home": 2, "away": 0}, "score": {"halftime": {"home": 0, "away": 0}, "fulltime": {"home": 2, "away": 0}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}], "expires_at": 1747914813.008772}