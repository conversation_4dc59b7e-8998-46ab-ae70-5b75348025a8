[flake8]
# Slightly more permissive than default 79
max-line-length = 120
exclude = .git,__pycache__,build,dist
ignore = E,W,F,E128,E302,E305,E501,W291,W293,F541
# Only enforce F401 (unused imports) and F821 (undefined names)
per-file-ignores =
    # Ignore import order in test files
    tests/*: E402
    # Ignore line length in test files
    tests/*: E501
    # Ignore blank line issues in test files
    tests/*: E302,E305
    # Ignore whitespace issues in test files
    tests/*: W293,W291
    # Ignore f-string issues in test files
    tests/*: F541
    # Ignore indentation issues in test files
    tests/*: E128
    # Ignore undefined names in test files
    tests/*: F821 