# Core FastAPI and web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Pydantic v2 (upgraded from v1)
pydantic==2.5.0
pydantic-settings==2.1.0

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
bcrypt==4.1.2

# HTTP client
aiohttp==3.9.1
httpx==0.25.2
requests==2.31.0

# Data processing
pandas==2.1.4
numpy==1.25.2
fastparquet==2023.8.0
joblib==1.3.2
scikit-learn==1.2.2
xgboost==1.7.5  # Enhanced ensemble models
lightgbm==3.3.5  # Enhanced ensemble models
catboost==1.2.2  # Additional gradient boosting library
tensorflow==2.15.0  # Neural networks and LSTM models
matplotlib==3.7.1
seaborn==0.12.2
pyarrow==12.0.1

# Database
sqlalchemy==2.0.23
alembic==1.12.1
python-multipart==0.0.6

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1

# Development tools
python-dotenv==1.0.0
python-telegram-bot==20.7
flake8==7.2.0
pre-commit==3.7.0
