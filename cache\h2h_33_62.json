{"data": [{"fixture": {"id": 1035461, "referee": "<PERSON><PERSON>", "timezone": "UTC", "date": "2024-04-24T19:00:00+00:00", "timestamp": 1713985200, "periods": {"first": 1713985200, "second": 1713988800}, "venue": {"id": 556, "name": "Old Trafford", "city": "Manchester"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 39, "name": "Premier League", "country": "England", "logo": "https://media.api-sports.io/football/leagues/39.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2023, "round": "Regular Season - 29", "standings": true}, "teams": {"home": {"id": 33, "name": "Manchester United", "logo": "https://media.api-sports.io/football/teams/33.png", "winner": true}, "away": {"id": 62, "name": "Sheffield Utd", "logo": "https://media.api-sports.io/football/teams/62.png", "winner": false}}, "goals": {"home": 4, "away": 2}, "score": {"halftime": {"home": 1, "away": 1}, "fulltime": {"home": 4, "away": 2}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}, {"fixture": {"id": 1035125, "referee": "<PERSON><PERSON>", "timezone": "UTC", "date": "2023-10-21T19:00:00+00:00", "timestamp": 1697914800, "periods": {"first": 1697914800, "second": 1697918400}, "venue": {"id": 581, "name": "Bramall Lane", "city": "Sheffield"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 39, "name": "Premier League", "country": "England", "logo": "https://media.api-sports.io/football/leagues/39.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2023, "round": "Regular Season - 9", "standings": true}, "teams": {"home": {"id": 62, "name": "Sheffield Utd", "logo": "https://media.api-sports.io/football/teams/62.png", "winner": false}, "away": {"id": 33, "name": "Manchester United", "logo": "https://media.api-sports.io/football/teams/33.png", "winner": true}}, "goals": {"home": 1, "away": 2}, "score": {"halftime": {"home": 1, "away": 1}, "fulltime": {"home": 1, "away": 2}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}, {"fixture": {"id": 592334, "referee": "<PERSON><PERSON>", "timezone": "UTC", "date": "2021-01-27T20:15:00+00:00", "timestamp": 1611778500, "periods": {"first": 1611778500, "second": 1611782100}, "venue": {"id": 556, "name": "Old Trafford", "city": "Manchester"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 39, "name": "Premier League", "country": "England", "logo": "https://media.api-sports.io/football/leagues/39.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2020, "round": "Regular Season - 20", "standings": true}, "teams": {"home": {"id": 33, "name": "Manchester United", "logo": "https://media.api-sports.io/football/teams/33.png", "winner": false}, "away": {"id": 62, "name": "Sheffield Utd", "logo": "https://media.api-sports.io/football/teams/62.png", "winner": true}}, "goals": {"home": 1, "away": 2}, "score": {"halftime": {"home": 0, "away": 1}, "fulltime": {"home": 1, "away": 2}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}, {"fixture": {"id": 592266, "referee": "<PERSON><PERSON>", "timezone": "UTC", "date": "2020-12-17T20:00:00+00:00", "timestamp": 1608235200, "periods": {"first": 1608235200, "second": 1608238800}, "venue": {"id": 581, "name": "Bramall Lane", "city": "Sheffield"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 39, "name": "Premier League", "country": "England", "logo": "https://media.api-sports.io/football/leagues/39.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2020, "round": "Regular Season - 13", "standings": true}, "teams": {"home": {"id": 62, "name": "Sheffield Utd", "logo": "https://media.api-sports.io/football/teams/62.png", "winner": false}, "away": {"id": 33, "name": "Manchester United", "logo": "https://media.api-sports.io/football/teams/33.png", "winner": true}}, "goals": {"home": 2, "away": 3}, "score": {"halftime": {"home": 1, "away": 2}, "fulltime": {"home": 2, "away": 3}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}, {"fixture": {"id": 157319, "referee": "<PERSON>, England", "timezone": "UTC", "date": "2020-06-24T17:00:00+00:00", "timestamp": 1593018000, "periods": {"first": 1593018000, "second": 1593021600}, "venue": {"id": 556, "name": "Old Trafford", "city": "Manchester"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 39, "name": "Premier League", "country": "England", "logo": "https://media.api-sports.io/football/leagues/39.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2019, "round": "Regular Season - 31", "standings": true}, "teams": {"home": {"id": 33, "name": "Manchester United", "logo": "https://media.api-sports.io/football/teams/33.png", "winner": true}, "away": {"id": 62, "name": "Sheffield Utd", "logo": "https://media.api-sports.io/football/teams/62.png", "winner": false}}, "goals": {"home": 3, "away": 0}, "score": {"halftime": {"home": 2, "away": 0}, "fulltime": {"home": 3, "away": 0}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}, {"fixture": {"id": 157142, "referee": "<PERSON>, England", "timezone": "UTC", "date": "2019-11-24T16:30:00+00:00", "timestamp": 1574613000, "periods": {"first": 1574613000, "second": 1574616600}, "venue": {"id": 581, "name": "Bramall Lane", "city": "Sheffield"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 39, "name": "Premier League", "country": "England", "logo": "https://media.api-sports.io/football/leagues/39.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2019, "round": "Regular Season - 13", "standings": true}, "teams": {"home": {"id": 62, "name": "Sheffield Utd", "logo": "https://media.api-sports.io/football/teams/62.png", "winner": null}, "away": {"id": 33, "name": "Manchester United", "logo": "https://media.api-sports.io/football/teams/33.png", "winner": null}}, "goals": {"home": 3, "away": 3}, "score": {"halftime": {"home": 1, "away": 0}, "fulltime": {"home": 3, "away": 3}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}, {"fixture": {"id": 448344, "referee": "<PERSON>, England", "timezone": "UTC", "date": "2016-01-09T17:30:00+00:00", "timestamp": 1452360600, "periods": {"first": 1452360600, "second": 1452364200}, "venue": {"id": 556, "name": "Old Trafford", "city": "Manchester"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 45, "name": "FA Cup", "country": "England", "logo": "https://media.api-sports.io/football/leagues/45.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2015, "round": "3rd Round", "standings": false}, "teams": {"home": {"id": 33, "name": "Manchester United", "logo": "https://media.api-sports.io/football/teams/33.png", "winner": true}, "away": {"id": 62, "name": "Sheffield Utd", "logo": "https://media.api-sports.io/football/teams/62.png", "winner": false}}, "goals": {"home": 1, "away": 0}, "score": {"halftime": {"home": 0, "away": 0}, "fulltime": {"home": 1, "away": 0}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}], "expires_at": 1747914879.436267}