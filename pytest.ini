[pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = --import-mode=importlib -v --tb=short
filterwarnings =
    ignore::DeprecationWarning
    ignore::UserWarning
    ignore::FutureWarning
    ignore::pydantic.warnings.PydanticDeprecatedSince20
    ignore::pydantic.warnings.PydanticDeprecationWarning
markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    slow: Slow running tests
    fast: Fast running tests 