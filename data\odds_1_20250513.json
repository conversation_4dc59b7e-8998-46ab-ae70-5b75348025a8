{"get": "odds", "parameters": {"bet": "1", "date": "2025-05-13"}, "errors": [], "results": 10, "paging": {"current": 1, "total": 9}, "response": [{"league": {"id": 368, "name": "Premier League", "country": "Singapore", "logo": "https://media.api-sports.io/football/leagues/368.png", "flag": "https://media.api-sports.io/flags/sg.svg", "season": 2024}, "fixture": {"id": 1196677, "timezone": "UTC", "date": "2025-05-13T11:45:00+00:00", "timestamp": 1747136700}, "update": "2025-05-13T08:00:31+00:00", "bookmakers": [{"id": 1, "name": "10Bet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "4.00"}, {"value": "Draw", "odd": "4.75"}, {"value": "Away", "odd": "1.57"}]}]}, {"id": 8, "name": "Bet365", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "4.75"}, {"value": "Draw", "odd": "4.20"}, {"value": "Away", "odd": "1.48"}]}]}, {"id": 2, "name": "Marathonbet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "4.05"}, {"value": "Draw", "odd": "5.15"}, {"value": "Away", "odd": "1.54"}]}]}, {"id": 16, "name": "Unibet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "3.90"}, {"value": "Draw", "odd": "4.35"}, {"value": "Away", "odd": "1.56"}]}]}, {"id": 3, "name": "Betfair", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "4.60"}, {"value": "Draw", "odd": "4.75"}, {"value": "Away", "odd": "1.47"}]}]}, {"id": 4, "name": "Pinnacle", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "3.33"}, {"value": "Draw", "odd": "5.62"}, {"value": "Away", "odd": "1.65"}]}]}, {"id": 11, "name": "1xBet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "4.04"}, {"value": "Draw", "odd": "5.15"}, {"value": "Away", "odd": "1.56"}]}]}, {"id": 32, "name": "Betano", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "3.95"}, {"value": "Draw", "odd": "4.50"}, {"value": "Away", "odd": "1.62"}]}]}, {"id": 34, "name": "Superbet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "3.90"}, {"value": "Draw", "odd": "4.75"}, {"value": "Away", "odd": "1.58"}]}]}, {"id": 22, "name": "Tipico", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "3.90"}, {"value": "Draw", "odd": "4.80"}, {"value": "Away", "odd": "1.55"}]}]}]}, {"league": {"id": 140, "name": "La Liga", "country": "Spain", "logo": "https://media.api-sports.io/football/leagues/140.png", "flag": "https://media.api-sports.io/flags/es.svg", "season": 2024}, "fixture": {"id": 1208813, "timezone": "UTC", "date": "2025-05-13T18:00:00+00:00", "timestamp": 1747159200}, "update": "2025-05-13T16:00:25+00:00", "bookmakers": [{"id": 1, "name": "10Bet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.32"}, {"value": "Draw", "odd": "3.25"}, {"value": "Away", "odd": "3.10"}]}]}, {"id": 7, "name": "<PERSON>", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.30"}, {"value": "Draw", "odd": "3.20"}, {"value": "Away", "odd": "3.10"}]}]}, {"id": 8, "name": "Bet365", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.30"}, {"value": "Draw", "odd": "3.30"}, {"value": "Away", "odd": "3.20"}]}]}, {"id": 2, "name": "Marathonbet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.32"}, {"value": "Draw", "odd": "3.30"}, {"value": "Away", "odd": "3.22"}]}]}, {"id": 16, "name": "Unibet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.32"}, {"value": "Draw", "odd": "3.25"}, {"value": "Away", "odd": "3.10"}]}]}, {"id": 3, "name": "Betfair", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.25"}, {"value": "Draw", "odd": "3.25"}, {"value": "Away", "odd": "3.10"}]}]}, {"id": 4, "name": "Pinnacle", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.34"}, {"value": "Draw", "odd": "3.38"}, {"value": "Away", "odd": "3.28"}]}]}, {"id": 5, "name": "SBO", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.32"}, {"value": "Draw", "odd": "3.14"}, {"value": "Away", "odd": "3.02"}]}]}, {"id": 11, "name": "1xBet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.39"}, {"value": "Draw", "odd": "3.40"}, {"value": "Away", "odd": "3.31"}]}]}, {"id": 32, "name": "Betano", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.30"}, {"value": "Draw", "odd": "3.30"}, {"value": "Away", "odd": "3.20"}]}]}, {"id": 34, "name": "Superbet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.35"}, {"value": "Draw", "odd": "3.30"}, {"value": "Away", "odd": "3.20"}]}]}, {"id": 22, "name": "Tipico", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.25"}, {"value": "Draw", "odd": "3.20"}, {"value": "Away", "odd": "3.30"}]}]}, {"id": 21, "name": "888Sport", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.30"}, {"value": "Draw", "odd": "3.20"}, {"value": "Away", "odd": "3.10"}]}]}, {"id": 9, "name": "Dafabet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.35"}, {"value": "Draw", "odd": "3.15"}, {"value": "Away", "odd": "3.25"}]}]}]}, {"league": {"id": 140, "name": "La Liga", "country": "Spain", "logo": "https://media.api-sports.io/football/leagues/140.png", "flag": "https://media.api-sports.io/flags/es.svg", "season": 2024}, "fixture": {"id": 1208814, "timezone": "UTC", "date": "2025-05-13T17:00:00+00:00", "timestamp": 1747155600}, "update": "2025-05-13T16:00:25+00:00", "bookmakers": [{"id": 1, "name": "10Bet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "4.45"}, {"value": "Draw", "odd": "3.80"}, {"value": "Away", "odd": "1.75"}]}]}, {"id": 7, "name": "<PERSON>", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "4.20"}, {"value": "Draw", "odd": "3.80"}, {"value": "Away", "odd": "1.75"}]}]}, {"id": 8, "name": "Bet365", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "4.50"}, {"value": "Draw", "odd": "4.10"}, {"value": "Away", "odd": "1.70"}]}]}, {"id": 2, "name": "Marathonbet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "4.65"}, {"value": "Draw", "odd": "4.00"}, {"value": "Away", "odd": "1.73"}]}]}, {"id": 16, "name": "Unibet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "4.40"}, {"value": "Draw", "odd": "3.85"}, {"value": "Away", "odd": "1.71"}]}]}, {"id": 3, "name": "Betfair", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "4.50"}, {"value": "Draw", "odd": "3.90"}, {"value": "Away", "odd": "1.70"}]}]}, {"id": 4, "name": "Pinnacle", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "4.81"}, {"value": "Draw", "odd": "4.09"}, {"value": "Away", "odd": "1.73"}]}]}, {"id": 5, "name": "SBO", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "4.37"}, {"value": "Draw", "odd": "3.82"}, {"value": "Away", "odd": "1.71"}]}]}, {"id": 11, "name": "1xBet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "4.78"}, {"value": "Draw", "odd": "4.11"}, {"value": "Away", "odd": "1.78"}]}]}, {"id": 32, "name": "Betano", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "4.55"}, {"value": "Draw", "odd": "4.05"}, {"value": "Away", "odd": "1.80"}]}]}, {"id": 34, "name": "Superbet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "4.45"}, {"value": "Draw", "odd": "4.00"}, {"value": "Away", "odd": "1.76"}]}]}, {"id": 22, "name": "Tipico", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "4.60"}, {"value": "Draw", "odd": "4.10"}, {"value": "Away", "odd": "1.67"}]}]}, {"id": 21, "name": "888Sport", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "4.20"}, {"value": "Draw", "odd": "3.80"}, {"value": "Away", "odd": "1.75"}]}]}, {"id": 9, "name": "Dafabet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "4.55"}, {"value": "Draw", "odd": "3.95"}, {"value": "Away", "odd": "1.73"}]}]}]}, {"league": {"id": 140, "name": "La Liga", "country": "Spain", "logo": "https://media.api-sports.io/football/leagues/140.png", "flag": "https://media.api-sports.io/flags/es.svg", "season": 2024}, "fixture": {"id": 1208816, "timezone": "UTC", "date": "2025-05-13T19:30:00+00:00", "timestamp": 1747164600}, "update": "2025-05-13T16:00:25+00:00", "bookmakers": [{"id": 1, "name": "10Bet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.66"}, {"value": "Draw", "odd": "3.70"}, {"value": "Away", "odd": "5.30"}]}]}, {"id": 7, "name": "<PERSON>", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.67"}, {"value": "Draw", "odd": "3.60"}, {"value": "Away", "odd": "5.00"}]}]}, {"id": 8, "name": "Bet365", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.65"}, {"value": "Draw", "odd": "3.70"}, {"value": "Away", "odd": "5.50"}]}]}, {"id": 2, "name": "Marathonbet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.67"}, {"value": "Draw", "odd": "3.75"}, {"value": "Away", "odd": "5.60"}]}]}, {"id": 16, "name": "Unibet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.64"}, {"value": "Draw", "odd": "3.65"}, {"value": "Away", "odd": "5.50"}]}]}, {"id": 3, "name": "Betfair", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.67"}, {"value": "Draw", "odd": "3.70"}, {"value": "Away", "odd": "5.50"}]}]}, {"id": 4, "name": "Pinnacle", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.68"}, {"value": "Draw", "odd": "3.76"}, {"value": "Away", "odd": "5.90"}]}]}, {"id": 5, "name": "SBO", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.67"}, {"value": "Draw", "odd": "3.59"}, {"value": "Away", "odd": "4.94"}]}]}, {"id": 11, "name": "1xBet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.72"}, {"value": "Draw", "odd": "3.86"}, {"value": "Away", "odd": "5.76"}]}]}, {"id": 32, "name": "Betano", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.75"}, {"value": "Draw", "odd": "3.65"}, {"value": "Away", "odd": "5.50"}]}]}, {"id": 34, "name": "Superbet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.70"}, {"value": "Draw", "odd": "3.80"}, {"value": "Away", "odd": "5.20"}]}]}, {"id": 22, "name": "Tipico", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.65"}, {"value": "Draw", "odd": "3.70"}, {"value": "Away", "odd": "5.50"}]}]}, {"id": 21, "name": "888Sport", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.67"}, {"value": "Draw", "odd": "3.60"}, {"value": "Away", "odd": "5.00"}]}]}, {"id": 9, "name": "Dafabet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.69"}, {"value": "Draw", "odd": "3.65"}, {"value": "Away", "odd": "5.40"}]}]}]}, {"league": {"id": 136, "name": "Serie B", "country": "Italy", "logo": "https://media.api-sports.io/football/leagues/136.png", "flag": "https://media.api-sports.io/flags/it.svg", "season": 2024}, "fixture": {"id": 1234960, "timezone": "UTC", "date": "2025-05-13T18:30:00+00:00", "timestamp": 1747161000}, "update": "2025-05-13T16:00:25+00:00", "bookmakers": [{"id": 1, "name": "10Bet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.40"}, {"value": "Draw", "odd": "4.33"}, {"value": "Away", "odd": "7.50"}]}]}, {"id": 7, "name": "<PERSON>", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.44"}, {"value": "Draw", "odd": "4.00"}, {"value": "Away", "odd": "7.50"}]}]}, {"id": 8, "name": "Bet365", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.42"}, {"value": "Draw", "odd": "4.33"}, {"value": "Away", "odd": "7.50"}]}]}, {"id": 2, "name": "Marathonbet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.42"}, {"value": "Draw", "odd": "4.40"}, {"value": "Away", "odd": "8.10"}]}]}, {"id": 16, "name": "Unibet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.40"}, {"value": "Draw", "odd": "4.30"}, {"value": "Away", "odd": "8.00"}]}]}, {"id": 3, "name": "Betfair", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.33"}, {"value": "Draw", "odd": "4.50"}, {"value": "Away", "odd": "8.50"}]}]}, {"id": 4, "name": "Pinnacle", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.39"}, {"value": "Draw", "odd": "4.41"}, {"value": "Away", "odd": "8.84"}]}]}, {"id": 5, "name": "SBO", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.37"}, {"value": "Draw", "odd": "4.12"}, {"value": "Away", "odd": "7.53"}]}]}, {"id": 11, "name": "1xBet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.43"}, {"value": "Draw", "odd": "4.40"}, {"value": "Away", "odd": "8.00"}]}]}, {"id": 32, "name": "Betano", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.44"}, {"value": "Draw", "odd": "4.30"}, {"value": "Away", "odd": "8.25"}]}]}, {"id": 34, "name": "Superbet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.43"}, {"value": "Draw", "odd": "4.65"}, {"value": "Away", "odd": "7.20"}]}]}, {"id": 22, "name": "Tipico", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.35"}, {"value": "Draw", "odd": "4.40"}, {"value": "Away", "odd": "8.20"}]}]}, {"id": 21, "name": "888Sport", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.44"}, {"value": "Draw", "odd": "4.00"}, {"value": "Away", "odd": "7.50"}]}]}, {"id": 9, "name": "Dafabet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.42"}, {"value": "Draw", "odd": "4.25"}, {"value": "Away", "odd": "7.20"}]}]}]}, {"league": {"id": 136, "name": "Serie B", "country": "Italy", "logo": "https://media.api-sports.io/football/leagues/136.png", "flag": "https://media.api-sports.io/flags/it.svg", "season": 2024}, "fixture": {"id": 1234961, "timezone": "UTC", "date": "2025-05-13T18:30:00+00:00", "timestamp": 1747161000}, "update": "2025-05-13T16:00:25+00:00", "bookmakers": [{"id": 1, "name": "10Bet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "3.10"}, {"value": "Draw", "odd": "3.00"}, {"value": "Away", "odd": "2.38"}]}]}, {"id": 7, "name": "<PERSON>", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "3.20"}, {"value": "Draw", "odd": "3.00"}, {"value": "Away", "odd": "2.38"}]}]}, {"id": 8, "name": "Bet365", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "3.30"}, {"value": "Draw", "odd": "3.00"}, {"value": "Away", "odd": "2.30"}]}]}, {"id": 2, "name": "Marathonbet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "3.26"}, {"value": "Draw", "odd": "3.08"}, {"value": "Away", "odd": "2.36"}]}]}, {"id": 16, "name": "Unibet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "3.15"}, {"value": "Draw", "odd": "3.00"}, {"value": "Away", "odd": "2.33"}]}]}, {"id": 3, "name": "Betfair", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "3.25"}, {"value": "Draw", "odd": "3.10"}, {"value": "Away", "odd": "2.30"}]}]}, {"id": 4, "name": "Pinnacle", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "3.30"}, {"value": "Draw", "odd": "3.10"}, {"value": "Away", "odd": "2.36"}]}]}, {"id": 5, "name": "SBO", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "3.11"}, {"value": "Draw", "odd": "2.88"}, {"value": "Away", "odd": "2.27"}]}]}, {"id": 11, "name": "1xBet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "3.26"}, {"value": "Draw", "odd": "3.08"}, {"value": "Away", "odd": "2.38"}]}]}, {"id": 32, "name": "Betano", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "3.25"}, {"value": "Draw", "odd": "3.05"}, {"value": "Away", "odd": "2.40"}]}]}, {"id": 34, "name": "Superbet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "3.25"}, {"value": "Draw", "odd": "3.10"}, {"value": "Away", "odd": "2.37"}]}]}, {"id": 22, "name": "Tipico", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "3.20"}, {"value": "Draw", "odd": "3.10"}, {"value": "Away", "odd": "2.25"}]}]}, {"id": 21, "name": "888Sport", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "3.20"}, {"value": "Draw", "odd": "3.00"}, {"value": "Away", "odd": "2.38"}]}]}, {"id": 9, "name": "Dafabet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "3.25"}, {"value": "Draw", "odd": "2.87"}, {"value": "Away", "odd": "2.35"}]}]}]}, {"league": {"id": 136, "name": "Serie B", "country": "Italy", "logo": "https://media.api-sports.io/football/leagues/136.png", "flag": "https://media.api-sports.io/flags/it.svg", "season": 2024}, "fixture": {"id": 1234962, "timezone": "UTC", "date": "2025-05-13T18:30:00+00:00", "timestamp": 1747161000}, "update": "2025-05-13T16:00:25+00:00", "bookmakers": [{"id": 1, "name": "10Bet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "3.30"}, {"value": "Draw", "odd": "3.00"}, {"value": "Away", "odd": "2.30"}]}]}, {"id": 7, "name": "<PERSON>", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "3.25"}, {"value": "Draw", "odd": "3.10"}, {"value": "Away", "odd": "2.20"}]}]}, {"id": 8, "name": "Bet365", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "3.60"}, {"value": "Draw", "odd": "3.00"}, {"value": "Away", "odd": "2.20"}]}]}, {"id": 2, "name": "Marathonbet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "3.50"}, {"value": "Draw", "odd": "3.24"}, {"value": "Away", "odd": "2.17"}]}]}, {"id": 16, "name": "Unibet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "3.60"}, {"value": "Draw", "odd": "3.05"}, {"value": "Away", "odd": "2.14"}]}]}, {"id": 3, "name": "Betfair", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "3.70"}, {"value": "Draw", "odd": "3.00"}, {"value": "Away", "odd": "2.10"}]}]}, {"id": 4, "name": "Pinnacle", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "3.58"}, {"value": "Draw", "odd": "3.14"}, {"value": "Away", "odd": "2.21"}]}]}, {"id": 5, "name": "SBO", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "3.35"}, {"value": "Draw", "odd": "2.76"}, {"value": "Away", "odd": "2.23"}]}]}, {"id": 11, "name": "1xBet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "3.48"}, {"value": "Draw", "odd": "3.24"}, {"value": "Away", "odd": "2.19"}]}]}, {"id": 32, "name": "Betano", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "3.60"}, {"value": "Draw", "odd": "3.10"}, {"value": "Away", "odd": "2.22"}]}]}, {"id": 34, "name": "Superbet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "3.55"}, {"value": "Draw", "odd": "3.10"}, {"value": "Away", "odd": "2.22"}]}]}, {"id": 22, "name": "Tipico", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "3.60"}, {"value": "Draw", "odd": "3.00"}, {"value": "Away", "odd": "2.10"}]}]}, {"id": 21, "name": "888Sport", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "3.25"}, {"value": "Draw", "odd": "3.10"}, {"value": "Away", "odd": "2.20"}]}]}, {"id": 9, "name": "Dafabet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "3.30"}, {"value": "Draw", "odd": "3.00"}, {"value": "Away", "odd": "2.25"}]}]}]}, {"league": {"id": 136, "name": "Serie B", "country": "Italy", "logo": "https://media.api-sports.io/football/leagues/136.png", "flag": "https://media.api-sports.io/flags/it.svg", "season": 2024}, "fixture": {"id": 1234963, "timezone": "UTC", "date": "2025-05-13T18:30:00+00:00", "timestamp": 1747161000}, "update": "2025-05-13T16:00:25+00:00", "bookmakers": [{"id": 1, "name": "10Bet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.05"}, {"value": "Draw", "odd": "3.20"}, {"value": "Away", "odd": "3.80"}]}]}, {"id": 7, "name": "<PERSON>", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.10"}, {"value": "Draw", "odd": "3.20"}, {"value": "Away", "odd": "3.40"}]}]}, {"id": 8, "name": "Bet365", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.00"}, {"value": "Draw", "odd": "3.20"}, {"value": "Away", "odd": "4.00"}]}]}, {"id": 2, "name": "Marathonbet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.08"}, {"value": "Draw", "odd": "3.22"}, {"value": "Away", "odd": "3.78"}]}]}, {"id": 16, "name": "Unibet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.02"}, {"value": "Draw", "odd": "3.05"}, {"value": "Away", "odd": "3.85"}]}]}, {"id": 3, "name": "Betfair", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.00"}, {"value": "Draw", "odd": "3.20"}, {"value": "Away", "odd": "3.90"}]}]}, {"id": 4, "name": "Pinnacle", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.07"}, {"value": "Draw", "odd": "3.29"}, {"value": "Away", "odd": "3.80"}]}]}, {"id": 5, "name": "SBO", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.03"}, {"value": "Draw", "odd": "2.95"}, {"value": "Away", "odd": "3.61"}]}]}, {"id": 11, "name": "1xBet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.10"}, {"value": "Draw", "odd": "3.22"}, {"value": "Away", "odd": "3.76"}]}]}, {"id": 32, "name": "Betano", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.07"}, {"value": "Draw", "odd": "3.25"}, {"value": "Away", "odd": "3.80"}]}]}, {"id": 34, "name": "Superbet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.10"}, {"value": "Draw", "odd": "3.30"}, {"value": "Away", "odd": "3.70"}]}]}, {"id": 22, "name": "Tipico", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.00"}, {"value": "Draw", "odd": "3.20"}, {"value": "Away", "odd": "3.70"}]}]}, {"id": 21, "name": "888Sport", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.10"}, {"value": "Draw", "odd": "3.20"}, {"value": "Away", "odd": "3.40"}]}]}, {"id": 9, "name": "Dafabet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.06"}, {"value": "Draw", "odd": "3.15"}, {"value": "Away", "odd": "3.35"}]}]}]}, {"league": {"id": 136, "name": "Serie B", "country": "Italy", "logo": "https://media.api-sports.io/football/leagues/136.png", "flag": "https://media.api-sports.io/flags/it.svg", "season": 2024}, "fixture": {"id": 1234964, "timezone": "UTC", "date": "2025-05-13T18:30:00+00:00", "timestamp": 1747161000}, "update": "2025-05-13T16:00:25+00:00", "bookmakers": [{"id": 1, "name": "10Bet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.85"}, {"value": "Draw", "odd": "2.90"}, {"value": "Away", "odd": "2.65"}]}]}, {"id": 7, "name": "<PERSON>", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.90"}, {"value": "Draw", "odd": "2.80"}, {"value": "Away", "odd": "2.50"}]}]}, {"id": 8, "name": "Bet365", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.75"}, {"value": "Draw", "odd": "3.00"}, {"value": "Away", "odd": "2.75"}]}]}, {"id": 2, "name": "Marathonbet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.94"}, {"value": "Draw", "odd": "2.79"}, {"value": "Away", "odd": "2.81"}]}]}, {"id": 16, "name": "Unibet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.85"}, {"value": "Draw", "odd": "3.00"}, {"value": "Away", "odd": "2.50"}]}]}, {"id": 3, "name": "Betfair", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.80"}, {"value": "Draw", "odd": "2.88"}, {"value": "Away", "odd": "2.70"}]}]}, {"id": 4, "name": "Pinnacle", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.84"}, {"value": "Draw", "odd": "2.95"}, {"value": "Away", "odd": "2.80"}]}]}, {"id": 5, "name": "SBO", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.71"}, {"value": "Draw", "odd": "2.87"}, {"value": "Away", "odd": "2.54"}]}]}, {"id": 11, "name": "1xBet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.94"}, {"value": "Draw", "odd": "2.79"}, {"value": "Away", "odd": "2.81"}]}]}, {"id": 32, "name": "Betano", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.80"}, {"value": "Draw", "odd": "2.95"}, {"value": "Away", "odd": "2.80"}]}]}, {"id": 34, "name": "Superbet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.87"}, {"value": "Draw", "odd": "3.00"}, {"value": "Away", "odd": "2.70"}]}]}, {"id": 22, "name": "Tipico", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.80"}, {"value": "Draw", "odd": "2.80"}, {"value": "Away", "odd": "2.70"}]}]}, {"id": 21, "name": "888Sport", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.90"}, {"value": "Draw", "odd": "2.80"}, {"value": "Away", "odd": "2.50"}]}]}, {"id": 9, "name": "Dafabet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "2.80"}, {"value": "Draw", "odd": "2.86"}, {"value": "Away", "odd": "2.68"}]}]}]}, {"league": {"id": 136, "name": "Serie B", "country": "Italy", "logo": "https://media.api-sports.io/football/leagues/136.png", "flag": "https://media.api-sports.io/flags/it.svg", "season": 2024}, "fixture": {"id": 1234965, "timezone": "UTC", "date": "2025-05-13T18:30:00+00:00", "timestamp": 1747161000}, "update": "2025-05-13T16:00:25+00:00", "bookmakers": [{"id": 1, "name": "10Bet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.53"}, {"value": "Draw", "odd": "4.20"}, {"value": "Away", "odd": "5.75"}]}]}, {"id": 7, "name": "<PERSON>", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.53"}, {"value": "Draw", "odd": "4.00"}, {"value": "Away", "odd": "5.80"}]}]}, {"id": 8, "name": "Bet365", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.50"}, {"value": "Draw", "odd": "4.33"}, {"value": "Away", "odd": "6.00"}]}]}, {"id": 2, "name": "Marathonbet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.52"}, {"value": "Draw", "odd": "4.30"}, {"value": "Away", "odd": "6.05"}]}]}, {"id": 16, "name": "Unibet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.54"}, {"value": "Draw", "odd": "3.80"}, {"value": "Away", "odd": "6.00"}]}]}, {"id": 3, "name": "Betfair", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.50"}, {"value": "Draw", "odd": "4.20"}, {"value": "Away", "odd": "5.50"}]}]}, {"id": 4, "name": "Pinnacle", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.52"}, {"value": "Draw", "odd": "4.39"}, {"value": "Away", "odd": "5.90"}]}]}, {"id": 5, "name": "SBO", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.47"}, {"value": "Draw", "odd": "4.01"}, {"value": "Away", "odd": "5.70"}]}]}, {"id": 11, "name": "1xBet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.54"}, {"value": "Draw", "odd": "4.30"}, {"value": "Away", "odd": "5.85"}]}]}, {"id": 32, "name": "Betano", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.60"}, {"value": "Draw", "odd": "4.40"}, {"value": "Away", "odd": "5.90"}]}]}, {"id": 34, "name": "Superbet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.54"}, {"value": "Draw", "odd": "4.40"}, {"value": "Away", "odd": "5.70"}]}]}, {"id": 22, "name": "Tipico", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.50"}, {"value": "Draw", "odd": "4.20"}, {"value": "Away", "odd": "5.50"}]}]}, {"id": 21, "name": "888Sport", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.53"}, {"value": "Draw", "odd": "4.00"}, {"value": "Away", "odd": "5.75"}]}]}, {"id": 9, "name": "Dafabet", "bets": [{"id": 1, "name": "Match Winner", "values": [{"value": "Home", "odd": "1.50"}, {"value": "Draw", "odd": "4.05"}, {"value": "Away", "odd": "6.00"}]}]}]}]}