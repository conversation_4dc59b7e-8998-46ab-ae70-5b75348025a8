{"data": [{"fixture": {"id": 1035418, "referee": "<PERSON><PERSON>", "timezone": "UTC", "date": "2024-02-18T16:30:00+00:00", "timestamp": 1708273800, "periods": {"first": 1708273800, "second": 1708277400}, "venue": {"id": 551, "name": "Kenilworth Road", "city": "Luton, Bedfordshire"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 39, "name": "Premier League", "country": "England", "logo": "https://media.api-sports.io/football/leagues/39.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2023, "round": "Regular Season - 25", "standings": true}, "teams": {"home": {"id": 1359, "name": "Luton", "logo": "https://media.api-sports.io/football/teams/1359.png", "winner": false}, "away": {"id": 33, "name": "Manchester United", "logo": "https://media.api-sports.io/football/teams/33.png", "winner": true}}, "goals": {"home": 1, "away": 2}, "score": {"halftime": {"home": 1, "away": 2}, "fulltime": {"home": 1, "away": 2}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}, {"fixture": {"id": 1035154, "referee": "<PERSON><PERSON>", "timezone": "UTC", "date": "2023-11-11T15:00:00+00:00", "timestamp": 1699714800, "periods": {"first": 1699714800, "second": 1699718400}, "venue": {"id": 556, "name": "Old Trafford", "city": "Manchester"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 39, "name": "Premier League", "country": "England", "logo": "https://media.api-sports.io/football/leagues/39.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2023, "round": "Regular Season - 12", "standings": true}, "teams": {"home": {"id": 33, "name": "Manchester United", "logo": "https://media.api-sports.io/football/teams/33.png", "winner": true}, "away": {"id": 1359, "name": "Luton", "logo": "https://media.api-sports.io/football/teams/1359.png", "winner": false}}, "goals": {"home": 1, "away": 0}, "score": {"halftime": {"home": 0, "away": 0}, "fulltime": {"home": 1, "away": 0}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}, {"fixture": {"id": 629210, "referee": "<PERSON>, England", "timezone": "UTC", "date": "2020-09-22T19:15:00+00:00", "timestamp": 1600802100, "periods": {"first": 1600802100, "second": 1600805700}, "venue": {"id": 551, "name": "Kenilworth Road", "city": "Luton, Bedfordshire"}, "status": {"long": "Match Finished", "short": "FT", "elapsed": 90, "extra": null}}, "league": {"id": 48, "name": "League Cup", "country": "England", "logo": "https://media.api-sports.io/football/leagues/48.png", "flag": "https://media.api-sports.io/flags/gb-eng.svg", "season": 2020, "round": "3rd Round", "standings": false}, "teams": {"home": {"id": 1359, "name": "Luton", "logo": "https://media.api-sports.io/football/teams/1359.png", "winner": false}, "away": {"id": 33, "name": "Manchester United", "logo": "https://media.api-sports.io/football/teams/33.png", "winner": true}}, "goals": {"home": 0, "away": 3}, "score": {"halftime": {"home": 0, "away": 1}, "fulltime": {"home": 0, "away": 3}, "extratime": {"home": null, "away": null}, "penalty": {"home": null, "away": null}}}], "expires_at": 1747914839.175029}